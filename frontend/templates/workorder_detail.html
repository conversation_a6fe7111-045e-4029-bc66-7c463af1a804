{% extends "base.html" %}

{% block title %}Work Order {{ workorder.wonum }} - Details{% endblock %}

{% block extra_css %}
<style>
    .workorder-detail-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .back-button {
        margin-bottom: 20px;
    }

    .workorder-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .workorder-header h1 {
        margin: 0;
        font-size: 2.5rem;
        font-weight: 300;
    }

    .workorder-header .subtitle {
        opacity: 0.9;
        font-size: 1.1rem;
        margin-top: 10px;
    }

    .status-badge {
        display: inline-block;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
    }

    /* Enhanced Tab Navigation */
    .nav-tabs-enhanced {
        border: none;
        background: #f8f9fa;
        border-radius: 12px;
        padding: 8px;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .nav-tabs-enhanced .nav-link {
        border: none;
        border-radius: 8px;
        padding: 1rem 1.5rem;
        margin: 0 4px;
        color: #6c757d;
        font-weight: 500;
        font-size: 1rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        background: transparent;
    }

    .nav-tabs-enhanced .nav-link:hover {
        background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
        color: #495057;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .nav-tabs-enhanced .nav-link.active {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        transform: translateY(-1px);
    }

    .nav-tabs-enhanced .nav-link i {
        font-size: 1.2rem;
        margin-right: 0.75rem;
        transition: transform 0.3s ease;
    }

    .nav-tabs-enhanced .nav-link:hover i {
        transform: scale(1.1);
    }

    .nav-tabs-enhanced .nav-link.active i {
        transform: scale(1.15);
    }

    /* Enhanced Tab Content */
    .tab-content-enhanced {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .tab-pane-enhanced {
        padding: 2rem;
        min-height: 400px;
    }

    /* Enhanced Detail Rows */
    .detail-row-enhanced {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 1rem 0;
        border-bottom: 1px solid #f1f3f4;
        transition: all 0.3s ease;
    }

    .detail-row-enhanced:hover {
        background: #f8f9fa;
        padding-left: 1rem;
        padding-right: 1rem;
        margin-left: -1rem;
        margin-right: -1rem;
        border-radius: 8px;
    }

    .detail-row-enhanced:last-child {
        border-bottom: none;
    }

    .detail-label-enhanced {
        font-weight: 600;
        color: #495057;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        min-width: 200px;
        margin-right: 1rem;
    }

    .detail-label-enhanced i {
        font-size: 1.3rem;
        margin-right: 0.75rem;
        color: #007bff;
        width: 20px;
        text-align: center;
    }

    .detail-value-enhanced {
        font-size: 1rem;
        color: #212529;
        font-weight: 500;
        flex: 1;
        text-align: right;
        word-wrap: break-word;
    }

    .detail-value-enhanced.empty {
        color: #6c757d;
        font-style: italic;
    }

    /* Task Pagination - Sticky */
    .task-pagination {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 12px;
        margin-bottom: 1rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        position: sticky;
        top: 0;
        z-index: 1020;
        border: 1px solid #dee2e6;
    }

    /* Sticky Task Header */
    .sticky-task-header {
        position: sticky;
        top: 80px; /* Below the pagination controls */
        z-index: 1019;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 1rem 1.5rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 12px rgba(102, 126, 234, 0.2);
        border-radius: 12px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .sticky-task-header:hover {
        box-shadow: 0 4px 16px rgba(102, 126, 234, 0.25);
        transform: translateY(-1px);
    }

    .sticky-task-header h5 {
        margin: 0;
        font-weight: 600;
        font-size: 1.1rem;
    }

    .sticky-task-header .task-description {
        margin: 0.5rem 0 0 0;
        font-size: 0.9rem;
        opacity: 0.9;
        line-height: 1.4;
    }

    /* Parent Work Order Section */
    .parent-wo-section {
        border-bottom: 1px solid rgba(255, 255, 255, 0.15);
        padding-bottom: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .parent-wo-title {
        margin: 0;
        font-weight: 600;
        font-size: 1rem;
        opacity: 0.95;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .parent-wo-description {
        font-size: 0.85rem;
        opacity: 0.85;
        line-height: 1.3;
        margin-top: 0.25rem;
        font-style: italic;
    }

    .task-section h5 {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    /* Desktop sticky header adjustments */
    @media (min-width: 769px) {
        .sticky-task-header {
            top: 90px; /* Optimized space for desktop pagination */
        }

        .task-pagination {
            top: 0;
            margin-bottom: 1rem;
        }

        .task-card-enhanced {
            margin-top: 1rem;
        }
    }

    /* Tablet adjustments */
    @media (max-width: 768px) and (min-width: 576px) {
        .sticky-task-header {
            top: 75px;
        }

        .sticky-task-header h5 {
            font-size: 1.05rem;
        }
    }

    .task-card-enhanced {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 1.5rem;
        overflow: hidden;
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
    }

    .task-card-enhanced:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: #007bff;
    }

    .task-header-enhanced {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 1.5rem;
        position: relative;
    }

    .task-header-enhanced::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #28a745, #20c997, #17a2b8);
    }

    .task-body-enhanced {
        padding: 1.5rem;
    }

    .task-actions-enhanced {
        background: #f8f9fa;
        padding: 1rem 1.5rem;
        border-top: 1px solid #e9ecef;
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
    }

    .task-action-btn {
        flex: 1;
        min-width: 120px;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        border: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .task-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .pagination-controls {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.75rem;
        margin-top: 1rem;
    }

    .pagination-btn {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
        padding: 0.6rem 1.2rem;
        border-radius: 20px;
        font-weight: 500;
        font-size: 0.9rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.25);
        display: flex;
        align-items: center;
        gap: 0.4rem;
        position: relative;
        overflow: hidden;
        min-width: 100px;
        justify-content: center;
    }

    .pagination-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .pagination-btn:hover:not(:disabled)::before {
        left: 100%;
    }

    .pagination-btn:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.35);
        background: linear-gradient(135deg, #5a67d8, #6b46c1);
    }

    .pagination-btn:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
    }

    .pagination-btn:disabled {
        background: linear-gradient(135deg, #e9ecef, #dee2e6);
        color: #6c757d;
        cursor: not-allowed;
        opacity: 0.7;
        transform: none;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .pagination-btn:disabled::before {
        display: none;
    }

    .pagination-info {
        background: rgba(255, 255, 255, 0.95);
        padding: 0.6rem 1.2rem;
        border-radius: 16px;
        font-weight: 600;
        font-size: 0.85rem;
        color: #495057;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(0, 0, 0, 0.05);
        backdrop-filter: blur(10px);
        white-space: nowrap;
    }

    /* Mobile Responsive Styles */
    @media (max-width: 768px) {
        /* Ensure single scrollbar for entire page */
        body {
            overflow-x: hidden;
            overflow-y: auto;
        }

        .workorder-detail-container {
            padding: 15px;
            /* Ensure container doesn't create its own scroll context */
            overflow: visible;
        }

        .workorder-header {
            padding: 20px;
            margin-bottom: 20px;
        }

        .workorder-header h1 {
            font-size: 1.8rem;
        }

        .nav-tabs-enhanced {
            padding: 6px;
            margin-bottom: 1rem;
        }

        .nav-tabs-enhanced .nav-link {
            padding: 0.75rem 1rem;
            font-size: 0.9rem;
            margin: 0 2px;
        }

        .nav-tabs-enhanced .nav-link i {
            font-size: 1.1rem;
            margin-right: 0.5rem;
        }

        .tab-pane-enhanced {
            padding: 1.5rem;
            min-height: 300px;
        }

        .detail-row-enhanced {
            flex-direction: column;
            align-items: flex-start;
            padding: 1.25rem 0;
        }

        .detail-label-enhanced {
            font-size: 1.2rem;
            min-width: auto;
            margin-right: 0;
            margin-bottom: 0.5rem;
        }

        .detail-label-enhanced i {
            font-size: 1.4rem;
            margin-right: 0.75rem;
        }

        .detail-value-enhanced {
            font-size: 1.1rem;
            text-align: left;
            padding-left: 2.5rem;
        }

        .task-header-enhanced {
            padding: 1.25rem;
        }

        .task-body-enhanced {
            padding: 1.25rem;
        }

        .task-actions-enhanced {
            flex-direction: column;
            padding: 1rem;
        }

        .task-action-btn {
            min-width: 100%;
            padding: 1rem;
            font-size: 1rem;
        }

        .pagination-controls {
            flex-direction: row;
            gap: 0.5rem;
            justify-content: space-between;
            margin-top: 0.75rem;
        }

        .pagination-btn {
            padding: 0.4rem 0.6rem;
            font-size: 0.75rem;
            flex: 1;
            max-width: 85px;
            min-width: 70px;
            border-radius: 12px;
        }

        .pagination-btn i {
            font-size: 0.65rem;
        }

        .pagination-info {
            padding: 0.4rem 0.6rem;
            font-size: 0.7rem;
            text-align: center;
            flex: 0 0 auto;
            white-space: nowrap;
            border-radius: 10px;
        }

        /* Mobile sticky header adjustments - Optimized for single scroll */
        .task-pagination {
            padding: 0.5rem;
            top: 0;
            margin-bottom: 0.25rem;
        }

        .sticky-task-header {
            top: 50px; /* Reduced for more compact layout */
            padding: 0.5rem 0.75rem;
            margin-bottom: 0.25rem;
        }

        .sticky-task-header h5 {
            font-size: 1rem;
        }

        .sticky-task-header .task-description {
            font-size: 0.85rem;
        }

        /* Ensure content doesn't hide behind sticky headers - Compact spacing */
        .task-card-enhanced {
            margin-top: 0.25rem;
            margin-bottom: 1rem; /* Reduced from default 1.5rem */
        }

        /* Hide parent work order section on mobile to save space */
        .parent-wo-section {
            display: none !important;
        }

        /* Mobile scroll optimization - Remove inner scrollbars */
        .tab-content-enhanced {
            /* Removed max-height and overflow-y to eliminate inner scrollbar */
            -webkit-overflow-scrolling: touch;
        }

        .task-card-enhanced {
            margin-bottom: 2rem;
        }
    }

    /* Enhanced Status Badge Styles with Better Contrast */
    .status-badge {
        font-weight: 600;
        font-size: 0.85rem;
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .status-ASSIGN {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
    }
    .status-APPR {
        background: linear-gradient(135deg, #28a745, #1e7e34);
        color: white;
        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
    }
    .status-READY {
        background: linear-gradient(135deg, #20c997, #17a2b8);
        color: white;
        box-shadow: 0 2px 4px rgba(32, 201, 151, 0.3);
    }
    .status-INPRG {
        background: linear-gradient(135deg, #fd7e14, #e55a00);
        color: white;
        box-shadow: 0 2px 4px rgba(253, 126, 20, 0.3);
    }
    .status-WMATL {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
        text-shadow: none;
        font-weight: 700;
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
    }
    .status-WAPPR {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
        text-shadow: none;
        font-weight: 700;
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
    }
    .status-WGOVT {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
        text-shadow: none;
        font-weight: 700;
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
    }
    .status-WSERV {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
        text-shadow: none;
        font-weight: 700;
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
    }
    .status-WSCH {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
        text-shadow: none;
        font-weight: 700;
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
    }
    .status-COMP {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: white;
        box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
    }
    .status-CLOSE {
        background: linear-gradient(135deg, #343a40, #23272b);
        color: white;
        box-shadow: 0 2px 4px rgba(52, 58, 64, 0.3);
    }
    .status-PACK {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: white;
        box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
    }

    /* Elegant Status Indicator Styles */
    .status-indicator-container {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.25rem;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .status-indicator-container:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .status-indicator-icon {
        font-size: 1.2rem !important;
        transition: all 0.3s ease;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }

    /* Status-specific colors with enhanced visibility */
    .status-indicator-icon.status-ASSIGN {
        color: #007bff;
        text-shadow: 0 0 8px rgba(0, 123, 255, 0.6);
    }
    .status-indicator-icon.status-APPR {
        color: #28a745;
        text-shadow: 0 0 8px rgba(40, 167, 69, 0.6);
    }
    .status-indicator-icon.status-READY {
        color: #20c997;
        text-shadow: 0 0 8px rgba(32, 201, 151, 0.6);
    }
    .status-indicator-icon.status-INPRG {
        color: #fd7e14;
        text-shadow: 0 0 8px rgba(253, 126, 20, 0.6);
    }
    .status-indicator-icon.status-WMATL {
        color: #ffc107;
        text-shadow: 0 0 8px rgba(255, 193, 7, 0.6);
    }
    .status-indicator-icon.status-WAPPR {
        color: #ffc107;
        text-shadow: 0 0 8px rgba(255, 193, 7, 0.6);
    }
    .status-indicator-icon.status-WGOVT {
        color: #ffc107;
        text-shadow: 0 0 8px rgba(255, 193, 7, 0.6);
    }
    .status-indicator-icon.status-WSERV {
        color: #ffc107;
        text-shadow: 0 0 8px rgba(255, 193, 7, 0.6);
    }
    .status-indicator-icon.status-WSCH {
        color: #ffc107;
        text-shadow: 0 0 8px rgba(255, 193, 7, 0.6);
    }
    .status-indicator-icon.status-COMP {
        color: #6c757d;
        text-shadow: 0 0 8px rgba(108, 117, 125, 0.6);
    }
    .status-indicator-icon.status-CLOSE {
        color: #343a40;
        text-shadow: 0 0 8px rgba(52, 58, 64, 0.6);
    }
    .status-indicator-icon.status-PACK {
        color: #6c757d;
        text-shadow: 0 0 8px rgba(108, 117, 125, 0.6);
    }
    .status-DEFER {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: white;
        box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
    }



    .task-action-section {
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 2px solid #e9ecef;
    }

    /* Enhanced Material Card Styles */
    .material-card-enhanced {
        background: linear-gradient(135deg, #ffffff, #f8f9fa);
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .material-card-enhanced::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #007bff, #28a745, #17a2b8);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .material-card-enhanced:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: #007bff;
    }

    .material-card-enhanced:hover::before {
        transform: scaleX(1);
    }

    .material-card-header-enhanced {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f1f3f4;
    }

    .material-item-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .material-icon {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        transition: all 0.3s ease;
    }

    .material-card-enhanced:hover .material-icon {
        transform: rotate(10deg) scale(1.1);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    }

    .material-itemnum-enhanced {
        font-weight: 700;
        color: #007bff;
        font-size: 1.1rem;
        margin: 0;
    }

    .material-description-enhanced {
        color: #6c757d;
        font-size: 0.9rem;
        margin: 0.25rem 0 0 0;
        line-height: 1.4;
    }

    .material-qty-badge {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }

    .material-card-enhanced:hover .material-qty-badge {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    }

    .material-details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .material-detail-item-enhanced {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .material-detail-item-enhanced:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }

    .material-detail-icon {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
        color: white;
        transition: all 0.3s ease;
    }

    .material-detail-item-enhanced:hover .material-detail-icon {
        transform: scale(1.1);
    }

    .material-detail-content {
        flex: 1;
    }

    .material-detail-label-enhanced {
        font-weight: 600;
        color: #495057;
        font-size: 0.8rem;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .material-detail-value-enhanced {
        color: #212529;
        font-weight: 500;
        font-size: 0.9rem;
        margin: 0.25rem 0 0 0;
    }

    /* Enhanced Labor Card Styles */
    .labor-card-enhanced {
        background: linear-gradient(135deg, #ffffff, #f8f9fa);
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .labor-card-enhanced::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #fd7e14, #ffc107, #20c997);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .labor-card-enhanced:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: #fd7e14;
    }

    .labor-card-enhanced:hover::before {
        transform: scaleX(1);
    }

    .labor-card-header-enhanced {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f1f3f4;
    }

    .labor-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .labor-icon {
        background: linear-gradient(135deg, #fd7e14, #e55a00);
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        transition: all 0.3s ease;
    }

    .labor-card-enhanced:hover .labor-icon {
        transform: rotate(-10deg) scale(1.1);
        box-shadow: 0 4px 12px rgba(253, 126, 20, 0.3);
    }

    .labor-code-enhanced {
        font-weight: 700;
        color: #fd7e14;
        font-size: 1.1rem;
        margin: 0;
    }

    .labor-craft-enhanced {
        color: #6c757d;
        font-size: 0.9rem;
        margin: 0.25rem 0 0 0;
    }

    .labor-hours-badge {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }

    .labor-card-enhanced:hover .labor-hours-badge {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
    }

    .labor-details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .labor-detail-item-enhanced {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .labor-detail-item-enhanced:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }

    .labor-detail-icon {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
        color: white;
        transition: all 0.3s ease;
    }

    .labor-detail-item-enhanced:hover .labor-detail-icon {
        transform: scale(1.1);
    }

    .labor-detail-content {
        flex: 1;
    }

    .labor-detail-label-enhanced {
        font-weight: 600;
        color: #495057;
        font-size: 0.8rem;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .labor-detail-value-enhanced {
        color: #212529;
        font-weight: 500;
        font-size: 0.9rem;
        margin: 0.25rem 0 0 0;
    }

    .labor-actions-enhanced {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
    }

    .labor-action-btn {
        padding: 0.5rem 1rem;
        border-radius: 6px;
        border: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .labor-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .priority-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .priority-1 { background-color: #e74c3c; }
    .priority-2 { background-color: #f39c12; }
    .priority-3 { background-color: #f1c40f; }
    .priority-4 { background-color: #2ecc71; }
    .priority-5 { background-color: #95a5a6; }

    /* Status Change Icon Styles */
    .status-change-icon {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        width: 28px;
        height: 28px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .status-change-icon:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        color: white;
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .status-change-icon i {
        font-size: 0.8rem;
    }

    /* Status Change Modal Styles */
    .modal-content {
        border: none;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px);
    }

    .modal-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-bottom: none;
        border-radius: 12px 12px 0 0;
        padding: 1rem 1.25rem;
    }

    .modal-title {
        font-weight: 600;
        font-size: 1rem;
        margin: 0;
    }

    .modal-body {
        padding: 1.25rem;
        background: white;
    }

    .modal-footer {
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
        border-radius: 0 0 12px 12px;
        padding: 1rem 1.25rem;
    }

    .btn-close {
        filter: brightness(0) invert(1);
        opacity: 0.8;
    }

    .btn-close:hover {
        opacity: 1;
    }

    #currentStatusDisplay {
        display: inline-block;
        font-size: 0.85rem;
        padding: 0.4rem 0.8rem;
    }

    /* Sticky Resource Tabs Styles */
    .sticky-resource-tabs {
        position: sticky;
        top: 140px; /* Below the sticky task header */
        z-index: 1018;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 1rem;
    }

    .resource-tabs {
        border: none;
        background: transparent;
        margin-bottom: 0;
    }

    .resource-tabs .nav-link {
        border: none;
        border-radius: 6px 6px 0 0;
        padding: 0.75rem 1rem;
        color: #6c757d;
        font-weight: 500;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        background: transparent;
        margin: 0 2px;
    }

    .resource-tabs .nav-link:hover {
        background: rgba(255, 255, 255, 0.7);
        color: #495057;
        transform: translateY(-1px);
    }

    /* Search buttons in tabs - Desktop */
    .resource-tabs .search-inventory-btn,
    .resource-tabs .search-labor-btn {
        padding: 0.25rem 0.5rem !important;
        font-size: 0.75rem !important;
        border: 2px solid #ffffff !important;
        background: rgba(0, 123, 255, 0.8) !important;
        color: white !important;
        transition: all 0.2s ease;
        min-width: 32px;
        height: 28px;
        display: flex !important;
        align-items: center;
        justify-content: center;
        border-radius: 4px !important;
    }

    .resource-tabs .search-inventory-btn:hover,
    .resource-tabs .search-labor-btn:hover {
        background: #0056b3 !important;
        border-color: #ffffff !important;
        transform: scale(1.1);
        color: white !important;
    }

    .resource-tabs .nav-link.active {
        background: white;
        color: #007bff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border-bottom: 2px solid #007bff;
    }

    .resource-tab-content {
        background: white;
        border-radius: 0 0 8px 8px;
        min-height: 200px;
    }

    .resource-content-area {
        padding: 1rem;
    }

    .resource-content-area h6 {
        color: #495057;
        font-weight: 600;
    }

    .load-materials-btn, .load-labor-btn {
        transition: all 0.3s ease;
    }

    .load-materials-btn:hover, .load-labor-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    /* Resource Pagination Controls */
    .resource-pagination-controls {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .pagination-nav-btn {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        border: 1px solid #007bff;
        background: white;
        color: #007bff;
    }

    .pagination-nav-btn:hover:not(:disabled) {
        background: #007bff;
        color: white;
        transform: scale(1.05);
    }

    .pagination-nav-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        border-color: #dee2e6;
        color: #6c757d;
    }

    .pagination-info-small {
        font-size: 0.85rem;
        font-weight: 600;
        color: #495057;
        min-width: 60px;
        text-align: center;
    }

    .detail-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        overflow: hidden;
    }


    .detail-card-header {
        background: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 1px solid #dee2e6;
        font-weight: 600;
        color: #495057;
    }

    .detail-card-body {
        padding: 20px;
    }

    .detail-row {
        display: flex;
        margin-bottom: 15px;
        align-items: center;
    }

    .detail-label {
        font-weight: 600;
        color: #6c757d;
        min-width: 150px;
        margin-right: 15px;
    }

    .detail-value {
        color: #495057;
        flex: 1;
    }

    .detail-value.empty {
        color: #adb5bd;
        font-style: italic;
    }

    .performance-info {
        background: #e8f5e8;
        border: 1px solid #c3e6c3;
        border-radius: 5px;
        padding: 10px;
        margin-top: 20px;
        font-size: 0.9rem;
        color: #155724;
    }

    .task-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        margin-bottom: 15px;
        overflow: hidden;
        transition: box-shadow 0.2s ease;
    }

    .task-card:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .task-header {
        background: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 1px solid #dee2e6;
    }

    .task-header .d-flex {
        min-height: 40px;
    }

    .task-body {
        padding: 15px 20px;
    }

    .task-title {
        font-weight: 600;
        color: #495057;
        margin: 0;
        flex: 1;
    }

    .task-actions {
        display: flex;
        gap: 10px;
        align-items: center;
    }



    .task-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 10px;
    }

    .task-info-item {
        display: flex;
        flex-direction: column;
    }

    .task-info-label {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 2px;
    }

    .task-info-value {
        font-weight: 500;
        color: #495057;
    }

    .no-tasks {
        text-align: center;
        padding: 40px 20px;
        color: #6c757d;
        font-style: italic;
    }

    .icon {
        margin-right: 8px;
        color: #6c757d;
    }

    .planned-materials-section {
        border-top: 1px solid #dee2e6;
        padding-top: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-top: 15px;
    }

    .planned-materials-section h6 {
        color: #495057;
        font-weight: 600;
    }

    .btn-group .btn {
        font-size: 0.875rem;
    }

    .materials-content {
        /* Removed max-height and overflow-y for mobile single-scroll optimization */
    }

    /* Desktop List View */
    .materials-desktop-view {
        display: block;
    }

    .materials-mobile-view {
        display: none;
    }

    .material-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 0.85rem;
    }

    .material-table th,
    .material-table td {
        padding: 8px 12px;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
        vertical-align: top;
    }

    .material-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .material-table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .material-itemnum {
        font-weight: 600;
        color: #0d6efd;
    }

    .material-description {
        max-width: 200px;
        word-wrap: break-word;
    }

    .material-cost {
        font-weight: 500;
        color: #198754;
    }

    .material-qty {
        font-weight: 500;
        color: #fd7e14;
    }

    /* Mobile Card View */
    @media (max-width: 768px) {
        .materials-desktop-view {
            display: none;
        }

        .materials-mobile-view {
            display: block;
        }

        /* Ensure no inner scrollbars on mobile */
        .materials-content {
            max-height: none !important;
            overflow-y: visible !important;
        }

        .material-card {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .material-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e9ecef;
        }

        .material-card-itemnum {
            font-weight: 600;
            color: #0d6efd;
            font-size: 1rem;
        }

        .material-card-qty {
            font-weight: 500;
            color: #fd7e14;
            font-size: 0.9rem;
        }

        .material-card-description {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .material-card-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 0.8rem;
        }

        .material-card-detail {
            display: flex;
            flex-direction: column;
        }

        .material-card-detail-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 2px;
        }

        .material-card-detail-value {
            color: #6c757d;
        }
    }

    .material-detail-item {
        display: flex;
        flex-direction: column;
    }

    .material-detail-label {
        color: #6c757d;
        font-size: 0.75rem;
        margin-bottom: 1px;
    }

    .material-detail-value {
        color: #495057;
        font-weight: 500;
    }

    .load-materials-btn {
        transition: all 0.2s ease;
    }

    .load-materials-btn:hover {
        transform: translateY(-1px);
    }

    .materials-loading {
        text-align: center;
        padding: 20px;
        color: #6c757d;
    }

    .materials-error {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 10px;
        border-radius: 5px;
        text-align: center;
    }

    .materials-empty {
        text-align: center;
        padding: 15px;
        color: #6c757d;
        font-style: italic;
    }

    /* Material Request Modal Styles */
    .item-result-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
        margin-bottom: 8px;
        transition: background-color 0.2s ease;
    }

    .item-result-card:hover {
        background: #e9ecef;
    }

    .item-results-container {
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
        background: white;
    }

    .request-material-btn {
        transition: all 0.2s ease;
    }

    .request-material-btn:hover {
        transform: translateY(-1px);
    }

    /* ===== COMPREHENSIVE RESPONSIVE DESIGN ===== */

    /* Extra Small Devices (phones, 576px and down) */
    @media (max-width: 575.98px) {
        .workorder-detail-container {
            padding: 8px;
            margin: 0;
        }

        .workorder-header {
            padding: 15px;
            margin-bottom: 15px;
        }

        .workorder-header .d-flex {
            flex-direction: column;
            align-items: flex-start !important;
            gap: 10px;
        }

        .workorder-header h1 {
            font-size: 1.5rem;
            line-height: 1.3;
        }

        .workorder-header .subtitle {
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .status-badge {
            padding: 6px 12px;
            font-size: 0.75rem;
        }

        .detail-card {
            margin-bottom: 15px;
        }

        .detail-card-header {
            padding: 12px 15px;
            font-size: 0.9rem;
        }

        .detail-card-body {
            padding: 15px;
        }

        .detail-row {
            flex-direction: column;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .detail-label {
            min-width: auto;
            margin-bottom: 4px;
            font-size: 0.85rem;
            font-weight: 700;
        }

        .detail-value {
            font-size: 0.9rem;
        }

        /* Task Cards Mobile */
        .task-card {
            margin-bottom: 12px;
        }

        .task-header {
            padding: 12px 15px;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .task-title {
            font-size: 0.9rem;
            line-height: 1.4;
            margin: 0;
            order: 2;
        }

        .toggle-task-btn {
            order: 1;
            align-self: flex-end;
            margin-bottom: 5px;
        }

        .task-actions {
            order: 3;
            width: 100%;
            flex-direction: column;
            gap: 8px;
        }



        .task-body {
            padding: 12px 15px;
        }

        .task-info {
            grid-template-columns: 1fr;
            gap: 10px;
        }

        .task-info-item {
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .task-info-label {
            font-size: 0.75rem;
        }

        .task-info-value {
            font-size: 0.85rem;
        }

        /* Materials Section Mobile */
        .planned-materials-section {
            padding: 12px;
            margin-top: 12px;
        }

        .planned-materials-section .d-flex {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .planned-materials-section h6 {
            font-size: 0.9rem;
        }

        .btn-group {
            width: 100%;
            flex-direction: column;
        }

        .btn-group .btn {
            width: 100%;
            margin-bottom: 5px;
            font-size: 0.8rem;
        }

        /* Back Button Mobile */
        .back-button {
            margin-bottom: 15px;
        }

        .back-button .btn {
            font-size: 0.85rem;
            padding: 8px 12px;
        }

        /* Extra small device sticky header - Ultra compact */
        .task-pagination {
            padding: 0.4rem;
            margin-bottom: 0.2rem;
        }

        .sticky-task-header {
            top: 45px; /* More compact for extra small screens */
            padding: 0.4rem 0.6rem;
            margin-bottom: 0.2rem;
        }

        .sticky-task-header h5 {
            font-size: 0.9rem;
            line-height: 1.3;
        }

        .sticky-task-header .task-description {
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }

        /* Keep the main header row horizontal on mobile like desktop */
        .sticky-task-header .d-flex.justify-content-between {
            flex-direction: row !important;
            align-items: center;
            gap: 0.5rem;
        }

        /* Only apply column layout to other flex containers if needed */
        .sticky-task-header .d-flex:not(.justify-content-between) {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .sticky-task-header .status-badge {
            font-size: 0.7rem;
            padding: 4px 8px;
        }

        /* Mobile status change icon - compact design */
        .status-change-icon {
            width: 24px;
            height: 24px;
            border-radius: 4px;
        }

        .status-change-icon i {
            font-size: 0.7rem;
        }

        /* Mobile sticky resource tabs */
        .sticky-resource-tabs {
            top: 100px; /* Adjusted for mobile sticky header */
            margin-bottom: 0.5rem;
            border-radius: 6px;
        }

        .resource-tabs .nav-link {
            padding: 0.5rem 0.75rem;
            font-size: 0.8rem;
        }

        /* Search buttons in tabs - Mobile */
        .resource-tabs .search-inventory-btn,
        .resource-tabs .search-labor-btn {
            padding: 0.2rem 0.4rem !important;
            font-size: 0.7rem !important;
            border: 2px solid #ffffff !important;
            background: rgba(0, 123, 255, 0.8) !important;
            color: white !important;
            min-width: 28px;
            height: 24px;
            display: flex !important;
            align-items: center;
            justify-content: center;
            border-radius: 4px !important;
        }

        .resource-tabs .search-inventory-btn:hover,
        .resource-tabs .search-labor-btn:hover {
            background: #0056b3 !important;
            border-color: #ffffff !important;
            color: white !important;
        }

        .resource-content-area {
            padding: 0.75rem;
        }

        .task-card-enhanced {
            margin-top: 0.2rem;
            margin-bottom: 0.8rem; /* Ultra compact spacing */
        }
    }

    /* Small Devices (landscape phones, 576px and up) */
    @media (min-width: 576px) and (max-width: 767.98px) {
        .workorder-detail-container {
            padding: 12px;
        }

        .workorder-header {
            padding: 20px;
        }

        .workorder-header h1 {
            font-size: 1.8rem;
        }

        .task-header {
            flex-wrap: wrap;
            gap: 10px;
        }

        .task-title {
            flex: 1;
            min-width: 200px;
        }

        .task-actions {
            flex-wrap: wrap;
            gap: 8px;
        }



        .task-info {
            grid-template-columns: repeat(2, 1fr);
        }

        .planned-materials-section .d-flex {
            flex-wrap: wrap;
            gap: 10px;
        }

        .btn-group {
            flex-wrap: wrap;
        }
    }

    /* Medium Devices (tablets, 768px and up) */
    @media (min-width: 768px) and (max-width: 991.98px) {
        .workorder-detail-container {
            padding: 15px;
        }

        .workorder-header h1 {
            font-size: 2.2rem;
        }

        .task-info {
            grid-template-columns: repeat(2, 1fr);
        }

        .task-actions {
            flex-wrap: wrap;
        }
    }

    /* Large Devices (desktops, 992px and up) */
    @media (min-width: 992px) and (max-width: 1199.98px) {
        .workorder-detail-container {
            max-width: 1000px;
        }

        .task-info {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    /* Extra Large Devices (large desktops, 1200px and up) */
    @media (min-width: 1200px) {
        .workorder-detail-container {
            max-width: 1200px;
        }

        .task-info {
            grid-template-columns: repeat(4, 1fr);
        }
    }

    /* Modal Responsive */
    @media (max-width: 575.98px) {
        .modal-dialog {
            margin: 10px;
            max-width: calc(100% - 20px);
        }

        .modal-body {
            padding: 15px;
        }

        .modal-header {
            padding: 15px;
        }

        .modal-footer {
            padding: 15px;
            flex-direction: column;
            gap: 10px;
        }

        .modal-footer .btn {
            width: 100%;
        }

        .row .col-md-8,
        .row .col-md-4,
        .row .col-md-6 {
            margin-bottom: 15px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="workorder-detail-container" data-site-id="{{ user_site_id if user_site_id else 'UNKNOWN' }}">
    <!-- Back Button -->
    <div class="back-button">
        <a href="{{ url_for('enhanced_workorders') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Work Orders
        </a>
    </div>

    <!-- Work Order Header -->
    <div class="workorder-header">
        <div class="d-flex flex-column flex-sm-row justify-content-between align-items-start">
            <div class="flex-grow-1">
                <h1 class="mb-2">
                    <span class="priority-indicator priority-{{ workorder.priority or 3 }}"></span>
                    <span class="d-inline d-sm-inline">Work Order {{ workorder.wonum }}</span>
                </h1>
                <div class="subtitle">{{ workorder.description or 'No description available' }}</div>
            </div>
            <div class="mt-2 mt-sm-0">
                <span class="status-badge status-{{ workorder.status }}">{{ workorder.status or 'Unknown' }}</span>
            </div>
        </div>
    </div>

    <!-- Enhanced Tab Navigation -->
    <ul class="nav nav-tabs nav-tabs-enhanced" id="workorderTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab" aria-controls="basic" aria-selected="true">
                <i class="fas fa-info-circle"></i>Basic Information
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="location-tab" data-bs-toggle="tab" data-bs-target="#location" type="button" role="tab" aria-controls="location" aria-selected="false">
                <i class="fas fa-map-marker-alt"></i>Location & Asset
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="scheduling-tab" data-bs-toggle="tab" data-bs-target="#scheduling" type="button" role="tab" aria-controls="scheduling" aria-selected="false">
                <i class="fas fa-calendar-alt"></i>Scheduling
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="tasks-tab" data-bs-toggle="tab" data-bs-target="#tasks" type="button" role="tab" aria-controls="tasks" aria-selected="false">
                <i class="fas fa-tasks"></i>Work Order Tasks
                {% if tasks %}
                    <span class="badge bg-light text-dark ms-2">{{ tasks|length }}</span>
                {% endif %}
            </button>
        </li>
    </ul>

    <!-- Enhanced Tab Content -->
    <div class="tab-content tab-content-enhanced" id="workorderTabContent">
        <!-- Basic Information Tab -->
        <div class="tab-pane fade show active tab-pane-enhanced" id="basic" role="tabpanel" aria-labelledby="basic-tab">
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-hashtag"></i>Work Order Number
                </div>
                <div class="detail-value-enhanced">{{ workorder.wonum }}</div>
            </div>
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-file-alt"></i>Description
                </div>
                <div class="detail-value-enhanced {{ 'empty' if not workorder.description }}">
                    {{ workorder.description or 'No description available' }}
                </div>
            </div>
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-flag"></i>Status
                </div>
                <div class="detail-value-enhanced">
                    <span class="status-badge status-{{ workorder.status }}">{{ workorder.status or 'Unknown' }}</span>
                </div>
            </div>
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-exclamation-triangle"></i>Priority
                </div>
                <div class="detail-value-enhanced">
                    <span class="priority-indicator priority-{{ workorder.priority or 3 }}"></span>
                    {{ workorder.priority or 'Not set' }}
                </div>
            </div>
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-tools"></i>Work Type
                </div>
                <div class="detail-value-enhanced {{ 'empty' if not workorder.worktype }}">
                    {{ workorder.worktype or 'Not specified' }}
                </div>
            </div>
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-building"></i>Site
                </div>
                <div class="detail-value-enhanced">{{ workorder.siteid or user_site_id }}</div>
            </div>
        </div>

        <!-- Location & Asset Tab -->
        <div class="tab-pane fade tab-pane-enhanced" id="location" role="tabpanel" aria-labelledby="location-tab">
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-map-pin"></i>Location
                </div>
                <div class="detail-value-enhanced {{ 'empty' if not workorder.location }}">
                    {{ workorder.location or 'No location specified' }}
                </div>
            </div>
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-cog"></i>Asset Number
                </div>
                <div class="detail-value-enhanced {{ 'empty' if not workorder.assetnum }}">
                    {{ workorder.assetnum or 'No asset assigned' }}
                </div>
            </div>
        </div>

        <!-- Scheduling Tab -->
        <div class="tab-pane fade tab-pane-enhanced" id="scheduling" role="tabpanel" aria-labelledby="scheduling-tab">
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-play-circle"></i>Target Start
                </div>
                <div class="detail-value-enhanced {{ 'empty' if not workorder.targetstart }}">
                    {{ workorder.targetstart or 'Not scheduled' }}
                </div>
            </div>
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-stop-circle"></i>Target Finish
                </div>
                <div class="detail-value-enhanced {{ 'empty' if not workorder.targetfinish }}">
                    {{ workorder.targetfinish or 'Not scheduled' }}
                </div>
            </div>
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-calendar-check"></i>Scheduled Start
                </div>
                <div class="detail-value-enhanced {{ 'empty' if not workorder.schedstart }}">
                    {{ workorder.schedstart or 'Not scheduled' }}
                </div>
            </div>
            <div class="detail-row-enhanced">
                <div class="detail-label-enhanced">
                    <i class="fas fa-calendar-times"></i>Scheduled Finish
                </div>
                <div class="detail-value-enhanced {{ 'empty' if not workorder.schedfinish }}">
                    {{ workorder.schedfinish or 'Not scheduled' }}
                </div>
            </div>
        </div>

        <!-- Tasks Tab -->
        <div class="tab-pane fade tab-pane-enhanced" id="tasks" role="tabpanel" aria-labelledby="tasks-tab">
            {% if tasks %}
                <!-- Task Pagination Controls -->
                <div class="task-pagination">
                    <div class="pagination-controls">
                        <button class="pagination-btn" id="prevTaskBtn" onclick="previousTask()" disabled>
                            <i class="fas fa-chevron-left"></i>Previous
                        </button>
                        <div class="pagination-info">
                            <span id="currentTaskInfo">Task 1 of {{ tasks|length }}</span>
                        </div>
                        <button class="pagination-btn" id="nextTaskBtn" onclick="nextTask()" {% if tasks|length <= 1 %}disabled{% endif %}>
                            Next<i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>

                <!-- Task Container -->
                <div id="taskContainer">
                    <!-- Tasks will be dynamically loaded here by JavaScript -->
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Tasks Found</h5>
                    <p class="text-muted">This work order doesn't have any associated tasks.</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Performance Information -->
    <div class="performance-info mt-4 p-3 bg-light rounded">
        <strong>Performance:</strong>
        Loaded in {{ "%.3f"|format(load_time) }}s using {{ auth_method }}
        {% if tasks %}
        <br><strong>Tasks:</strong> {{ tasks|length }} tasks loaded
        {% endif %}
    </div>

</div>

<!-- Status Change Modal -->
<div class="modal fade" id="statusChangeModal" tabindex="-1" aria-labelledby="statusChangeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="statusChangeModalLabel">
                    <i class="fas fa-edit me-2"></i>Change Task Status
                </h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label small text-muted">Current Status:</label>
                    <div id="currentStatusDisplay" class="status-badge mb-3">UNKNOWN</div>
                </div>
                <div class="mb-3">
                    <label for="newStatusSelect" class="form-label small text-muted">New Status:</label>
                    <select class="form-select" id="newStatusSelect">
                        <option value="">Select new status...</option>
                        <option value="ASSIGN">Assign</option>
                        <option value="APPR">Approve</option>
                        <option value="INPRG">In Progress</option>
                        <option value="WMATL">Waiting for Material</option>
                        <option value="COMP">Complete</option>
                        <option value="CLOSE">Close</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-primary btn-sm" id="confirmStatusChange" disabled>
                    <i class="fas fa-save me-1"></i>Update Status
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Hidden Tasks Data for JavaScript -->
<script type="application/json" id="tasksData">
{{ tasks | tojson | safe }}
</script>

<!-- Hidden Work Order Data for JavaScript -->
<script type="application/json" id="workorderData">
{{ workorder | tojson | safe }}
</script>





<script>
// Global variables
let currentSiteId = null;
let tasksData = [];
let workorderData = null;
let currentTaskIndex = 0;



// Status Change Modal Functions
let currentTaskWonum = null;
let statusChangeModal = null;

function openStatusChangeModal(taskWonum, currentStatus) {
    currentTaskWonum = taskWonum;

    // Update modal content
    const currentStatusDisplay = document.getElementById('currentStatusDisplay');
    currentStatusDisplay.className = `status-badge status-${currentStatus}`;
    currentStatusDisplay.textContent = currentStatus || 'Unknown';

    // Reset and populate the select dropdown
    const newStatusSelect = document.getElementById('newStatusSelect');
    newStatusSelect.value = '';

    // Disable current status option
    const options = newStatusSelect.querySelectorAll('option');
    options.forEach(option => {
        option.disabled = option.value === currentStatus;
    });

    // Reset confirm button
    document.getElementById('confirmStatusChange').disabled = true;

    // Show modal using the global instance
    if (statusChangeModal) {
        statusChangeModal.show();
    }
}

function updateTaskStatusFromModal(taskWonum, newStatus) {
    const confirmBtn = document.getElementById('confirmStatusChange');
    const originalText = confirmBtn.innerHTML;

    // Show loading state
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Updating...';
    confirmBtn.disabled = true;

    // Make API call
    fetch(`/api/task/${taskWonum}/status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            status: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the status badge in the sticky header
            const statusBadge = document.querySelector('.sticky-task-header .status-badge');
            if (statusBadge) {
                statusBadge.className = `status-badge status-${newStatus}`;
                statusBadge.textContent = newStatus;
            }

            // Update the status change icon data
            const statusIcon = document.querySelector(`[data-task-wonum="${taskWonum}"]`);
            if (statusIcon) {
                statusIcon.setAttribute('data-current-status', newStatus);
            }

            // Update the status indicator icon color
            const statusIndicatorIcon = document.querySelector(`[data-task-status="${taskWonum}"] .status-indicator-icon`);
            if (statusIndicatorIcon) {
                // Remove all status classes
                statusIndicatorIcon.className = statusIndicatorIcon.className.replace(/status-\w+/g, '');
                // Add new status class
                statusIndicatorIcon.classList.add(`status-${newStatus}`);
            }

            // Also update the container's data attribute and title
            const statusIndicatorContainer = document.querySelector(`[data-task-status="${taskWonum}"]`);
            if (statusIndicatorContainer) {
                statusIndicatorContainer.setAttribute('data-task-status', newStatus);
                statusIndicatorContainer.setAttribute('title', `Task Status: ${newStatus}`);
            }

            // Update task data
            const currentTask = tasksData[currentTaskIndex];
            if (currentTask && currentTask.wonum === taskWonum) {
                currentTask.status = newStatus;
            }

            // Close modal
            if (statusChangeModal) {
                statusChangeModal.hide();
            }

            // Show success message
            showNotification('success', data.message);
        } else {
            showNotification('error', data.error || 'Failed to update task status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('error', 'Network error occurred while updating task status');
    })
    .finally(() => {
        // Restore button state
        confirmBtn.innerHTML = originalText;
        confirmBtn.disabled = false;
    });
}

// Move navigation functions outside DOMContentLoaded to make them immediately available
// Pagination variables for materials and labor
var materialsPagination = {};
var laborPagination = {};

// Materials pagination navigation - GLOBAL FUNCTION
function navigateMaterials(taskWonum, direction) {
    console.log('🔄 MATERIALS NAV: Navigating', direction, 'for task', taskWonum);
    console.log('🔄 MATERIALS NAV: Current pagination state:', materialsPagination[taskWonum]);

    // Show loading on navigation buttons
    const navButtons = document.querySelectorAll(`.materials-prev-btn[data-task-wonum="${taskWonum}"], .materials-next-btn[data-task-wonum="${taskWonum}"]`);
    console.log('🔄 MATERIALS NAV: Found nav buttons:', navButtons.length);

    navButtons.forEach(btn => {
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        btn.disabled = true;
    });

    const pagination = materialsPagination[taskWonum];
    if (!pagination) {
        console.error('❌ MATERIALS NAV: No pagination found for task', taskWonum);
        console.error('❌ MATERIALS NAV: Available pagination objects:', Object.keys(materialsPagination));
        // Restore buttons
        navButtons.forEach((btn, index) => {
            btn.innerHTML = index === 0 ? '<i class="fas fa-chevron-left"></i>' : '<i class="fas fa-chevron-right"></i>';
            btn.disabled = false;
        });
        return;
    }

    const totalPages = Math.ceil(pagination.totalItems / pagination.itemsPerPage);
    console.log('📄 MATERIALS NAV: Current page:', pagination.currentPage, 'Total pages:', totalPages);

    if (direction === 'prev' && pagination.currentPage > 0) {
        pagination.currentPage--;
    } else if (direction === 'next' && pagination.currentPage < totalPages - 1) {
        pagination.currentPage++;
    }

    console.log('📄 MATERIALS NAV: New page:', pagination.currentPage);

    // Reload materials with new pagination
    const loadBtn = document.querySelector(`[data-task-wonum="${taskWonum}"].load-materials-btn`);
    if (loadBtn) {
        loadPlannedMaterials(taskWonum, loadBtn.getAttribute('data-task-status'), loadBtn);
    } else {
        console.error('❌ MATERIALS NAV: Load button not found for task', taskWonum);
        // Restore buttons if load fails
        restoreNavigationButtons(taskWonum, 'materials');
    }
}

// Labor pagination navigation - GLOBAL FUNCTION
function navigateLabor(taskWonum, direction) {
    console.log('🔄 LABOR NAV: Navigating', direction, 'for task', taskWonum);
    console.log('🔄 LABOR NAV: Current pagination state:', laborPagination[taskWonum]);

    // Show loading on navigation buttons
    const navButtons = document.querySelectorAll(`.labor-prev-btn[data-task-wonum="${taskWonum}"], .labor-next-btn[data-task-wonum="${taskWonum}"]`);
    console.log('🔄 LABOR NAV: Found nav buttons:', navButtons.length);

    navButtons.forEach(btn => {
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        btn.disabled = true;
    });

    const pagination = laborPagination[taskWonum];
    if (!pagination) {
        console.error('❌ LABOR NAV: No pagination found for task', taskWonum);
        console.error('❌ LABOR NAV: Available pagination objects:', Object.keys(laborPagination));
        // Restore buttons
        navButtons.forEach((btn, index) => {
            btn.innerHTML = index === 0 ? '<i class="fas fa-chevron-left"></i>' : '<i class="fas fa-chevron-right"></i>';
            btn.disabled = false;
        });
        return;
    }

    const totalPages = Math.ceil(pagination.totalItems / pagination.itemsPerPage);
    console.log('📄 LABOR NAV: Current page:', pagination.currentPage, 'Total pages:', totalPages);

    if (direction === 'prev' && pagination.currentPage > 0) {
        pagination.currentPage--;
    } else if (direction === 'next' && pagination.currentPage < totalPages - 1) {
        pagination.currentPage++;
    }

    console.log('📄 LABOR NAV: New page:', pagination.currentPage);

    // Reload labor with new pagination
    const loadBtn = document.querySelector(`[data-task-wonum="${taskWonum}"].load-labor-btn`);
    if (loadBtn) {
        loadTaskLabor(taskWonum, loadBtn.getAttribute('data-task-status'), loadBtn);
    } else {
        console.error('❌ LABOR NAV: Load button not found for task', taskWonum);
        // Restore buttons if load fails
        restoreNavigationButtons(taskWonum, 'labor');
    }
}

// Function to restore navigation buttons after loading
function restoreNavigationButtons(taskWonum, type) {
    const prevClass = type === 'materials' ? '.materials-prev-btn' : '.labor-prev-btn';
    const nextClass = type === 'materials' ? '.materials-next-btn' : '.labor-next-btn';

    const prevButtons = document.querySelectorAll(`${prevClass}[data-task-wonum="${taskWonum}"]`);
    const nextButtons = document.querySelectorAll(`${nextClass}[data-task-wonum="${taskWonum}"]`);

    // Restore button content and enable them
    prevButtons.forEach(btn => {
        btn.innerHTML = '<i class="fas fa-chevron-left"></i>';
        btn.disabled = false;
    });

    nextButtons.forEach(btn => {
        btn.innerHTML = '<i class="fas fa-chevron-right"></i>';
        btn.disabled = false;
    });

    // Re-enable based on pagination state
    const pagination = type === 'materials' ? materialsPagination[taskWonum] : laborPagination[taskWonum];
    if (pagination) {
        const totalPages = Math.ceil(pagination.totalItems / pagination.itemsPerPage);

        prevButtons.forEach(btn => {
            btn.disabled = pagination.currentPage === 0;
        });

        nextButtons.forEach(btn => {
            btn.disabled = pagination.currentPage >= totalPages - 1;
        });
    }
}

// Make functions globally accessible immediately
window.navigateMaterials = navigateMaterials;
window.navigateLabor = navigateLabor;
window.restoreNavigationButtons = restoreNavigationButtons;

// Debug function to test if functions are working
window.testNavigation = function() {
    console.log('🧪 TEST: Navigation functions available');
    console.log('🧪 TEST: navigateMaterials:', typeof window.navigateMaterials);
    console.log('🧪 TEST: navigateLabor:', typeof window.navigateLabor);
    console.log('🧪 TEST: materialsPagination:', materialsPagination);
    console.log('🧪 TEST: laborPagination:', laborPagination);

    // Test if we can find navigation buttons
    console.log('🧪 TEST: Materials nav buttons:', document.querySelectorAll('.materials-prev-btn, .materials-next-btn').length);
    console.log('🧪 TEST: Labor nav buttons:', document.querySelectorAll('.labor-prev-btn, .labor-next-btn').length);

    // Test calling navigation directly
    console.log('🧪 TEST: Attempting to call navigateMaterials...');
    try {
        if (Object.keys(materialsPagination).length > 0) {
            const firstTask = Object.keys(materialsPagination)[0];
            console.log('🧪 TEST: Calling navigateMaterials for task:', firstTask);
            navigateMaterials(firstTask, 'next');
        } else {
            console.log('🧪 TEST: No materials pagination available');
        }
    } catch(e) {
        console.error('🧪 TEST: Error calling navigateMaterials:', e);
    }
};

// Enhanced task pagination and display functionality
document.addEventListener('DOMContentLoaded', function() {
    // Get site ID from data attribute
    const container = document.querySelector('.workorder-detail-container');
    currentSiteId = container ? container.getAttribute('data-site-id') : 'UNKNOWN';
    console.log('Current Site ID:', currentSiteId);

    // Load tasks data from JSON
    const tasksDataElement = document.getElementById('tasksData');
    if (tasksDataElement) {
        try {
            tasksData = JSON.parse(tasksDataElement.textContent);
            console.log('Loaded tasks data:', tasksData);

            // Initialize task display if tasks exist
            if (tasksData && tasksData.length > 0) {
                displayCurrentTask();
            }
        } catch (error) {
            console.error('Error parsing tasks data:', error);
        }
    }

    // Load work order data from JSON
    const workorderDataElement = document.getElementById('workorderData');
    if (workorderDataElement) {
        try {
            workorderData = JSON.parse(workorderDataElement.textContent);
            console.log('Loaded work order data:', workorderData);
        } catch (error) {
            console.error('Error parsing work order data:', error);
        }
    }

    // Initialize event handlers for dynamic content
    initializeEventHandlers();

    // Initialize status change modal
    statusChangeModal = new bootstrap.Modal(document.getElementById('statusChangeModal'), {
        backdrop: true,
        keyboard: true,
        focus: true
    });

    // Initialize modal event listeners
    document.getElementById('newStatusSelect').addEventListener('change', function() {
        const confirmBtn = document.getElementById('confirmStatusChange');
        confirmBtn.disabled = !this.value;
    });

    document.getElementById('confirmStatusChange').addEventListener('click', function() {
        const newStatus = document.getElementById('newStatusSelect').value;
        if (newStatus && currentTaskWonum) {
            updateTaskStatusFromModal(currentTaskWonum, newStatus);
        }
    });

    // Handle modal hidden event to reset state
    document.getElementById('statusChangeModal').addEventListener('hidden.bs.modal', function() {
        currentTaskWonum = null;
        document.getElementById('newStatusSelect').value = '';
        document.getElementById('confirmStatusChange').disabled = true;
    });
});

// Task pagination functions
function nextTask() {
    if (currentTaskIndex < tasksData.length - 1) {
        currentTaskIndex++;
        displayCurrentTask();
    }
}

function previousTask() {
    if (currentTaskIndex > 0) {
        currentTaskIndex--;
        displayCurrentTask();
    }
}

function displayCurrentTask() {
    if (!tasksData || tasksData.length === 0) return;

    const task = tasksData[currentTaskIndex];
    const container = document.getElementById('taskContainer');

    if (!container) return;

    // Update pagination info
    document.getElementById('currentTaskInfo').textContent = `Task ${currentTaskIndex + 1} of ${tasksData.length}`;

    // Update pagination buttons
    document.getElementById('prevTaskBtn').disabled = currentTaskIndex === 0;
    document.getElementById('nextTaskBtn').disabled = currentTaskIndex === tasksData.length - 1;

    // Generate sticky task header and task card HTML
    container.innerHTML = generateStickyTaskHeader(task) + generateTaskCardHTML(task);

    // Reinitialize event handlers for the new content
    initializeEventHandlers();
}

function generateStickyTaskHeader(task) {
    const parentWO = workorderData || {};

    // Only show parent WO section if we have valid parent work order data
    const hasValidParentWO = parentWO.wonum && parentWO.wonum !== 'N/A' && parentWO.wonum !== '';

    let parentWOSection = '';
    if (hasValidParentWO) {
        parentWOSection = `
            <!-- Parent Work Order Section (Desktop Only) -->
            <div class="parent-wo-section d-none d-md-block mb-2">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-1 parent-wo-title">
                            <i class="fas fa-folder-open me-2"></i>
                            Parent WO: ${parentWO.wonum}
                            <span class="status-badge status-${parentWO.status} ms-2">${parentWO.status || 'Unknown'}</span>
                        </h6>
                        <div class="parent-wo-description">
                            ${parentWO.description || 'No description available'}
                        </div>
                    </div>
                </div>
                <hr class="my-2 border-light opacity-50">
            </div>`;
    }

    return `
        <div class="sticky-task-header">
            ${parentWOSection}

            <!-- Task Section -->
            <div class="task-section">
                <!-- Header Row with Status Left and Change Icon Right -->
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div class="d-flex align-items-center gap-2">
                        <span class="status-badge status-${task.status}">${task.status || 'Unknown'}</span>
                        <div class="priority-indicator priority-${task.priority || 3}"></div>
                    </div>
                    <button class="btn btn-sm status-change-icon" data-task-wonum="${task.wonum}" data-current-status="${task.status}" title="Change Status">
                        <i class="fas fa-edit"></i>
                    </button>
                    <div class="status-indicator-container ms-2" data-task-status="${task.status}" title="Task Status: ${task.status}">
                        <i class="fas fa-circle-dot status-indicator-icon status-${task.status}"></i>
                    </div>
                    <button class="btn btn-sm btn-warning ms-1" onclick="window.testNavigation()" title="Debug Navigation">
                        <i class="fas fa-bug"></i> DEBUG
                    </button>
                </div>

                <!-- Task Info Row -->
                <div class="task-info-row">
                    <h5 class="mb-1">
                        <i class="fas fa-clipboard-list me-2"></i>
                        Task WO: ${task.wonum}
                        <span class="badge bg-light text-dark ms-2">
                            <i class="fas fa-hashtag me-1"></i>ID: ${task.taskid}
                        </span>
                    </h5>
                    <div class="task-description">
                        ${task.description || 'No description available'}
                    </div>
                </div>
            </div>

            <!-- Sticky Materials and Labor Tabs -->
            <div class="sticky-resource-tabs mt-3">
                <ul class="nav nav-tabs resource-tabs" id="resourceTabs-${task.wonum}" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active d-flex align-items-center justify-content-between" id="materials-tab-${task.wonum}" data-bs-toggle="tab" data-bs-target="#materials-content-${task.wonum}" type="button" role="tab">
                            <span><i class="fas fa-boxes me-2"></i>Materials</span>
                            ${['APPR', 'ASSIGN', 'WMATL', 'INPRG'].includes(task.status) ? `
                            <button type="button" class="btn btn-sm btn-outline-light ms-2 search-inventory-btn" onclick="event.stopPropagation(); window.openInventorySearchForTask('${task.siteid || 'UNKNOWN'}', '${task.parent || task.wonum}', '${task.wonum}', ${task.taskid})" title="Search Inventory">
                                <i class="fas fa-search"></i>
                            </button>
                            ` : ''}
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link d-flex align-items-center justify-content-between" id="labor-tab-${task.wonum}" data-bs-toggle="tab" data-bs-target="#labor-content-${task.wonum}" type="button" role="tab">
                            <span><i class="fas fa-users me-2"></i>Labor</span>
                            ${['APPR', 'ASSIGN', 'WMATL', 'INPRG', 'READY', 'COMP'].includes(task.status) ? `
                            <button type="button" class="btn btn-sm btn-outline-light ms-2 search-labor-btn" onclick="event.stopPropagation(); window.openLaborSearchForTask('${task.siteid || 'UNKNOWN'}', '${task.parent || task.wonum}', '${task.wonum}', ${task.taskid})" title="Search Labor">
                                <i class="fas fa-search"></i>
                            </button>
                            ` : ''}
                        </button>
                    </li>
                </ul>
                <div class="tab-content resource-tab-content" id="resourceTabContent-${task.wonum}">
                    <div class="tab-pane fade show active" id="materials-content-${task.wonum}" role="tabpanel">
                        <div class="resource-content-area">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">
                                    <i class="fas fa-boxes me-2"></i>Planned Materials
                                </h6>
                                <button class="btn btn-sm btn-outline-primary load-materials-btn" data-task-wonum="${task.wonum}" data-task-status="${task.status}">
                                    <i class="fas fa-download me-1"></i>Load Materials
                                </button>
                            </div>
                            <div class="materials-content" id="materials-content-${task.wonum}">
                                <div class="text-center text-muted py-3">
                                    <i class="fas fa-box-open fa-2x mb-2"></i>
                                    <div>Click "Load Materials" to view planned materials</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="labor-content-${task.wonum}" role="tabpanel">
                        <div class="resource-content-area">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">
                                    <i class="fas fa-users me-2"></i>Labor Resources
                                </h6>
                                <button class="btn btn-sm btn-outline-primary load-labor-btn" data-task-wonum="${task.wonum}" data-task-status="${task.status}">
                                    <i class="fas fa-download me-1"></i>Load Labor
                                </button>
                            </div>
                            <div class="labor-content" id="labor-content-${task.wonum}"
                                 data-task-id="${task.taskid}"
                                 data-site-id="${task.siteid}"
                                 data-task-wonum="${task.wonum}"
                                 data-parent-wonum="${task.parent || task.wonum}">
                                <div class="text-center text-muted py-3">
                                    <i class="fas fa-user-hard-hat fa-2x mb-2"></i>
                                    <div>Click "Load Labor" to view labor resources</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function generateTaskCardHTML(task) {
    return `
        <div class="task-card-enhanced">

            <div class="task-body-enhanced">
                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-tools"></i>Work Type
                    </div>
                    <div class="detail-value-enhanced">${task.worktype || 'Not specified'}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-user"></i>Owner
                    </div>
                    <div class="detail-value-enhanced">${task.owner || 'Not Assigned'}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-users"></i>Owner Group
                    </div>
                    <div class="detail-value-enhanced">${task.owner_group || 'Not Assigned'}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-user-tie"></i>Lead
                    </div>
                    <div class="detail-value-enhanced">${task.lead || 'Not Assigned'}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-user-check"></i>Supervisor
                    </div>
                    <div class="detail-value-enhanced">${task.supervisor || 'Not Assigned'}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-users-cog"></i>Crew
                    </div>
                    <div class="detail-value-enhanced">${task.crew || 'Not Assigned'}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-map-pin"></i>Location
                    </div>
                    <div class="detail-value-enhanced">${task.location || 'Not specified'}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-cog"></i>Asset
                    </div>
                    <div class="detail-value-enhanced">${task.assetnum || 'No asset'}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-calendar-check"></i>Scheduled Start
                    </div>
                    <div class="detail-value-enhanced">${task.schedstart || 'Not scheduled'}</div>
                </div>

                <div class="detail-row-enhanced">
                    <div class="detail-label-enhanced">
                        <i class="fas fa-clock"></i>Estimated Duration
                    </div>
                    <div class="detail-value-enhanced">${task.estdur || 'Not estimated'}</div>
                </div>
            </div>


        </div>
    `;
}

function initializeEventHandlers() {
    // Handle status change icon clicks (new popup functionality)
    document.querySelectorAll('.status-change-icon').forEach(button => {
        button.addEventListener('click', function() {
            const taskWonum = this.getAttribute('data-task-wonum');
            const currentStatus = this.getAttribute('data-current-status');
            openStatusChangeModal(taskWonum, currentStatus);
        });
    });



    // Handle planned materials loading
    document.querySelectorAll('.load-materials-btn').forEach(button => {
        button.addEventListener('click', function() {
            const taskWonum = this.getAttribute('data-task-wonum');
            const taskStatus = this.getAttribute('data-task-status');
            loadPlannedMaterials(taskWonum, taskStatus, this);
        });
    });

    // Handle labor loading
    document.querySelectorAll('.load-labor-btn').forEach(button => {
        button.addEventListener('click', function() {
            const taskWonum = this.getAttribute('data-task-wonum');
            const taskStatus = this.getAttribute('data-task-status');
            loadTaskLabor(taskWonum, taskStatus, this);
        });
    });


}

// Global function to open inventory search for a specific task
function openInventorySearchForTask(siteId, parentWonum, taskWonum, taskId) {
    // Store task context for material requests
    // parentWonum = parent work order (e.g. 2021-1744762)
    // taskWonum = task work order number (e.g. 2021-1835482)
    // taskId = actual numeric task ID from the task (e.g. 10, 20, 30, etc.)
    if (typeof materialRequestManager !== 'undefined') {
        materialRequestManager.setTaskContext(parentWonum, taskWonum, taskId);
    }

    // Call the regular inventory search
    openInventorySearch(siteId);
}

// Global function to open labor search for a specific task
function openLaborSearchForTask(siteId, parentWonum, taskWonum, taskId) {
    // Store task context for labor requests
    // parentWonum = parent work order (e.g. 2021-1744762)
    // taskWonum = task work order number (e.g. 2021-1835482)
    // taskId = actual numeric task ID from the task (e.g. 10, 20, 30, etc.)
    if (typeof laborSearchManager !== 'undefined') {
        laborSearchManager.setTaskContext(parentWonum, taskWonum, taskId);
    }

    // Call the regular labor search
    openLaborSearch(siteId);
}

// Utility function to format currency properly - USE ONLY REAL MAXIMO DATA
function formatCurrency(amount, currencyCode) {
    // If no real data from Maximo, return "No cost data"
    if (!currencyCode || amount === null || amount === undefined) {
        return 'No cost data';
    }

    const value = parseFloat(amount);
    if (isNaN(value)) {
        return 'No cost data';
    }

    if (currencyCode === 'USD') {
        return `$${value.toFixed(2)}`;
    } else if (currencyCode === 'EUR') {
        return `€${value.toFixed(2)}`;
    } else if (currencyCode === 'GBP') {
        return `£${value.toFixed(2)}`;
    } else {
        return `${currencyCode} ${value.toFixed(2)}`;
    }
}

// Function to format date/time values
function formatDateTime(dateString) {
    if (!dateString) return 'Not set';
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    } catch (e) {
        return dateString; // Return as-is if parsing fails
    }
}

// Function to generate comprehensive materials display with pagination
function generateMaterialsDisplay(materials, taskWonum = null) {
    // Initialize pagination for this task if not exists
    if (taskWonum && !materialsPagination[taskWonum]) {
        materialsPagination[taskWonum] = {
            currentPage: 0,
            itemsPerPage: window.innerWidth <= 768 ? 1 : materials.length, // 1 item per page on mobile
            totalItems: materials.length
        };
    }

    const pagination = taskWonum ? materialsPagination[taskWonum] : null;
    const isMobile = window.innerWidth <= 768;

    // Get materials for current page
    let displayMaterials = materials;
    if (pagination && isMobile) {
        const startIndex = pagination.currentPage * pagination.itemsPerPage;
        const endIndex = startIndex + pagination.itemsPerPage;
        displayMaterials = materials.slice(startIndex, endIndex);
    }
    // Desktop table view
    let desktopHtml = `
        <div class="materials-desktop-view">
            <table class="material-table">
                <thead>
                    <tr>
                        <th>Item Number</th>
                        <th>Description</th>
                        <th>Qty</th>
                        <th>Unit Cost</th>
                        <th>Line Cost</th>
                        <th>Vendor</th>
                        <th>Store Location</th>
                        <th>Direct Request</th>
                        <th>Requested By</th>
                        <th>Required Date</th>
                    </tr>
                </thead>
                <tbody>
    `;

    displayMaterials.forEach(material => {
        desktopHtml += `
            <tr>
                <td class="material-itemnum">${material.itemnum || 'N/A'}</td>
                <td class="material-description" title="${material.description_longdescription || material.description || 'No description'}">${material.description || 'No description'}</td>
                <td class="material-qty">${material.itemqty || 0} ${material.unit || 'EA'}</td>
                <td class="material-cost">${formatCurrency(material.unitcost || 0, 'USD')}</td>
                <td class="material-cost">${formatCurrency(material.linecost || 0, 'USD')}</td>
                <td>${material.vendor || 'N/A'}</td>
                <td>${material.storeloc || 'N/A'}</td>
                <td>${material.directreq ? '<span class="badge bg-warning">Yes</span>' : '<span class="badge bg-success">No</span>'}</td>
                <td>${material.requestby || 'N/A'}</td>
                <td>${formatDateTime(material.requiredate)}</td>
            </tr>
        `;
    });

    desktopHtml += `
                </tbody>
            </table>
        </div>
    `;

    // Mobile card view with pagination
    let mobileHtml = `<div class="materials-mobile-view">`;

    // Add pagination controls for mobile
    if (pagination && isMobile && materials.length > 1) {
        const totalPages = Math.ceil(materials.length / pagination.itemsPerPage);
        const prevDisabled = pagination.currentPage === 0 ? 'disabled' : '';
        const nextDisabled = pagination.currentPage >= totalPages - 1 ? 'disabled' : '';

        mobileHtml += `
            <div class="resource-pagination-controls mb-3">
                <button class="btn btn-sm btn-outline-primary pagination-nav-btn materials-prev-btn"
                        data-task-wonum="${taskWonum}"
                        data-direction="prev"
                        ${prevDisabled}>
                    <i class="fas fa-chevron-left"></i>
                </button>
                <span class="pagination-info-small">
                    ${pagination.currentPage + 1} of ${totalPages}
                </span>
                <button class="btn btn-sm btn-outline-primary pagination-nav-btn materials-next-btn"
                        data-task-wonum="${taskWonum}"
                        data-direction="next"
                        ${nextDisabled}>
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        `;
    }

    displayMaterials.forEach(material => {
        mobileHtml += `
            <div class="material-card-enhanced">
                <div class="material-card-header-enhanced">
                    <div class="material-item-info">
                        <div class="material-icon">
                            <i class="fas fa-cube"></i>
                        </div>
                        <div>
                            <h6 class="material-itemnum-enhanced">${material.itemnum || 'N/A'}</h6>
                            <p class="material-description-enhanced">${material.description || 'No description available'}</p>
                        </div>
                    </div>
                    <div class="material-qty-badge">
                        <i class="fas fa-boxes"></i>
                        ${material.itemqty || 0} ${material.unit || 'EA'}
                    </div>
                </div>

                <div class="material-details-grid">
                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #28a745, #20c997);">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Unit Cost</p>
                            <p class="material-detail-value-enhanced">${formatCurrency(material.unitcost || 0, 'USD')}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #17a2b8, #138496);">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Line Cost</p>
                            <p class="material-detail-value-enhanced">${formatCurrency(material.linecost || 0, 'USD')}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #6f42c1, #5a2d91);">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Vendor</p>
                            <p class="material-detail-value-enhanced">${material.vendor || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #fd7e14, #e55a00);">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Store Location</p>
                            <p class="material-detail-value-enhanced">${material.storeloc || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, ${material.directreq ? '#dc3545, #c82333' : '#28a745, #1e7e34'});">
                            <i class="fas ${material.directreq ? 'fa-exclamation-triangle' : 'fa-check-circle'}"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Direct Request</p>
                            <p class="material-detail-value-enhanced">${material.directreq ? 'Yes' : 'No'}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #6c757d, #495057);">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Requested By</p>
                            <p class="material-detail-value-enhanced">${material.requestby || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #e83e8c, #d91a72);">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Required Date</p>
                            <p class="material-detail-value-enhanced">${formatDateTime(material.requiredate)}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #20c997, #17a2b8);">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Line Type</p>
                            <p class="material-detail-value-enhanced">${material.linetype_description || material.linetype || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #ffc107, #e0a800);">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Condition Code</p>
                            <p class="material-detail-value-enhanced">${material.conditioncode || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="material-detail-item-enhanced">
                        <div class="material-detail-icon" style="background: linear-gradient(135deg, #007bff, #0056b3);">
                            <i class="fas fa-bookmark"></i>
                        </div>
                        <div class="material-detail-content">
                            <p class="material-detail-label-enhanced">Reservation Type</p>
                            <p class="material-detail-value-enhanced">${material.restype_description || material.restype || 'N/A'}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    mobileHtml += `</div>`;

    return desktopHtml + mobileHtml;
}



function loadPlannedMaterials(taskWonum, taskStatus, button) {
    // Show loading state
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Loading...';
    button.disabled = true;

    const materialsContent = document.getElementById(`materials-content-${taskWonum}`);

    // Show enhanced loading in content area
    materialsContent.innerHTML = `
        <div class="materials-loading text-center py-4">
            <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="h5 text-primary mb-2">Loading Planned Materials</div>
            <div class="text-muted">Please wait while we fetch the materials data...</div>
            <div class="progress mt-3" style="height: 6px;">
                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
            </div>
        </div>
    `;

    // Make API call
    fetch(`/api/task/${taskWonum}/planned-materials?status=${taskStatus}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.show_materials) {
            if (data.materials && data.materials.length > 0) {
                // Display materials with comprehensive data and pagination
                const materialsHtml = generateMaterialsDisplay(data.materials, taskWonum);
                materialsContent.innerHTML = materialsHtml;

                // Update button to show refresh option
                button.innerHTML = '<i class="fas fa-sync-alt me-1"></i>Refresh';
                button.disabled = false;

                // Restore navigation buttons
                restoreNavigationButtons(taskWonum, 'materials');

                // Add event listeners for materials navigation buttons
                setTimeout(() => {
                    const materialsNavButtons = document.querySelectorAll('.materials-prev-btn, .materials-next-btn');
                    materialsNavButtons.forEach(btn => {
                        btn.addEventListener('click', function() {
                            const taskWonum = this.getAttribute('data-task-wonum');
                            const direction = this.getAttribute('data-direction');
                            console.log('🔄 MATERIALS NAV CLICK:', direction, 'for task', taskWonum);
                            navigateMaterials(taskWonum, direction);
                        });
                    });
                }, 100);

                showNotification('success', `Loaded ${data.materials.length} planned materials for task ${taskWonum}`);
            } else {
                // No materials found
                materialsContent.innerHTML = `
                    <div class="materials-empty">
                        <i class="fas fa-box-open fa-2x mb-2"></i>
                        <div>No planned materials found for this task</div>
                    </div>
                `;
                button.innerHTML = originalText;
                button.disabled = false;
            }
        } else {
            // Materials not available for this status
            materialsContent.innerHTML = `
                <div class="materials-empty">
                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                    <div>${data.message || 'Planned materials not available for this task status'}</div>
                </div>
            `;
            button.innerHTML = originalText;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error loading planned materials:', error);
        materialsContent.innerHTML = `
            <div class="materials-error">
                <i class="fas fa-exclamation-triangle me-1"></i>
                Error loading planned materials: ${error.message || 'Unknown error'}
            </div>
        `;
        button.innerHTML = originalText;
        button.disabled = false;
        showNotification('error', 'Failed to load planned materials');
    });
}
function loadTaskLabor(taskWonum, taskStatus, button) {
    // Show loading state
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Loading...';
    button.disabled = true;

    const laborContent = document.getElementById(`labor-content-${taskWonum}`);

    // Show enhanced loading in content area
    laborContent.innerHTML = `
        <div class="labor-loading text-center py-4">
            <div class="spinner-border text-success mb-3" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="h5 text-success mb-2">Loading Labor Resources</div>
            <div class="text-muted">Please wait while we fetch the labor data...</div>
            <div class="progress mt-3" style="height: 6px;">
                <div class="progress-bar bg-success progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
            </div>
        </div>
    `;

    // Make API call to get labor data
    fetch(`/api/task/${taskWonum}/labor?status=${taskStatus}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log(`👷 LABOR LOAD: Response for task ${taskWonum}:`, data);

        if (data.success && data.show_labor) {
            if (data.labor && data.labor.length > 0) {
                // Display labor with comprehensive data and pagination
                const laborHtml = generateLaborDisplay(data.labor, taskWonum);
                laborContent.innerHTML = laborHtml;

                // Update button to show refresh option
                button.innerHTML = '<i class="fas fa-sync-alt me-1"></i>Refresh';
                button.disabled = false;

                // Restore navigation buttons
                restoreNavigationButtons(taskWonum, 'labor');

                // Add event listeners for labor navigation buttons
                setTimeout(() => {
                    const laborNavButtons = document.querySelectorAll('.labor-prev-btn, .labor-next-btn');
                    laborNavButtons.forEach(btn => {
                        btn.addEventListener('click', function() {
                            const taskWonum = this.getAttribute('data-task-wonum');
                            const direction = this.getAttribute('data-direction');
                            console.log('🔄 LABOR NAV CLICK:', direction, 'for task', taskWonum);
                            navigateLabor(taskWonum, direction);
                        });
                    });
                }, 100);

                showNotification('success', `Loaded ${data.labor.length} labor assignments for task ${taskWonum}`);
                console.log(`✅ LABOR LOAD: Successfully loaded ${data.labor.length} labor records for task ${taskWonum}`);
            } else {
                // No labor found
                laborContent.innerHTML = `
                    <div class="labor-empty">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <div>No labor assignments found for this task</div>
                        <small class="text-muted">Task ${taskWonum} has no labor records</small>
                    </div>
                `;
                button.innerHTML = originalText;
                button.disabled = false;
                console.log(`ℹ️ LABOR LOAD: No labor records found for task ${taskWonum}`);
            }
        } else {
            // Labor not available or error occurred
            laborContent.innerHTML = `
                <div class="labor-empty">
                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                    <div>${data.message || 'Labor assignments not available for this task'}</div>
                    <small class="text-muted">Error: ${data.error || 'Unknown error'}</small>
                </div>
            `;
            button.innerHTML = originalText;
            button.disabled = false;
            console.log(`❌ LABOR LOAD: Failed to load labor for task ${taskWonum}:`, data.error);
        }
    })
    .catch(error => {
        console.error(`❌ LABOR LOAD: Network error loading labor for task ${taskWonum}:`, error);
        laborContent.innerHTML = `
            <div class="labor-error">
                <i class="fas fa-exclamation-triangle me-1"></i>
                Error loading labor assignments: ${error.message || 'Network error'}
            </div>
        `;
        button.innerHTML = originalText;
        button.disabled = false;
        showNotification('error', 'Failed to load labor assignments');
    });
}

// Function to generate comprehensive labor display with pagination
function generateLaborDisplay(laborRecords, taskWonum = null) {
    // Initialize pagination for this task if not exists
    if (taskWonum && !laborPagination[taskWonum]) {
        laborPagination[taskWonum] = {
            currentPage: 0,
            itemsPerPage: window.innerWidth <= 768 ? 1 : laborRecords.length, // 1 item per page on mobile
            totalItems: laborRecords.length
        };
    }

    const pagination = taskWonum ? laborPagination[taskWonum] : null;
    const isMobile = window.innerWidth <= 768;

    // Get labor records for current page
    let displayLabor = laborRecords;
    if (pagination && isMobile) {
        const startIndex = pagination.currentPage * pagination.itemsPerPage;
        const endIndex = startIndex + pagination.itemsPerPage;
        displayLabor = laborRecords.slice(startIndex, endIndex);
    }
    // Desktop table view
    let desktopHtml = `
        <div class="labor-desktop-view d-none d-lg-block">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Labor Code</th>
                        <th>Hours</th>
                        <th>Craft</th>
                        <th>Start Date</th>
                        <th>Finish Date</th>
                        <th>Regular Hrs</th>
                        <th>Premium Hrs</th>
                        <th>Transaction Type</th>
                        <th>Trans ID</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    displayLabor.forEach(labor => {
        desktopHtml += `
            <tr>
                <td class="labor-code"><strong>${labor.laborcode || 'N/A'}</strong></td>
                <td class="labor-hours">${labor.laborhrs || 0} hrs</td>
                <td class="labor-craft">${labor.craft || 'N/A'}</td>
                <td class="labor-start">${formatDateTime(labor.startdate)}</td>
                <td class="labor-finish">${formatDateTime(labor.finishdate)}</td>
                <td class="labor-regular">${labor.regularhrs || 0} hrs</td>
                <td class="labor-premium">${labor.premiumpayhours || 0} hrs</td>
                <td class="labor-transtype">${labor.transtype || 'N/A'}</td>
                <td class="labor-transid">${labor.labtransid || 'N/A'}</td>
                <td class="labor-actions">
                    <button class="btn btn-sm btn-outline-warning delete-labor-btn"
                            data-labor-id="${labor.labtransid || ''}"
                            data-labor-code="${labor.laborcode || ''}"
                            data-labor-hours="${labor.regularhrs || 0}"
                            onclick="deleteLaborEntry(this)"
                            title="Add negative hours to offset this entry">
                        <i class="fas fa-minus me-1"></i>Subtract Hours
                    </button>
                </td>
            </tr>
        `;
    });

    desktopHtml += `
                </tbody>
            </table>
        </div>
    `;

    // Mobile card view with pagination
    let mobileHtml = `<div class="labor-mobile-view d-lg-none">`;

    // Add pagination controls for mobile
    if (pagination && isMobile && laborRecords.length > 1) {
        const totalPages = Math.ceil(laborRecords.length / pagination.itemsPerPage);
        const prevDisabled = pagination.currentPage === 0 ? 'disabled' : '';
        const nextDisabled = pagination.currentPage >= totalPages - 1 ? 'disabled' : '';

        mobileHtml += `
            <div class="resource-pagination-controls mb-3">
                <button class="btn btn-sm btn-outline-primary pagination-nav-btn labor-prev-btn"
                        data-task-wonum="${taskWonum}"
                        data-direction="prev"
                        ${prevDisabled}>
                    <i class="fas fa-chevron-left"></i>
                </button>
                <span class="pagination-info-small">
                    ${pagination.currentPage + 1} of ${totalPages}
                </span>
                <button class="btn btn-sm btn-outline-primary pagination-nav-btn labor-next-btn"
                        data-task-wonum="${taskWonum}"
                        data-direction="next"
                        ${nextDisabled}>
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        `;
    }

    displayLabor.forEach(labor => {
        mobileHtml += `
            <div class="labor-card-enhanced">
                <div class="labor-card-header-enhanced">
                    <div class="labor-info">
                        <div class="labor-icon">
                            <i class="fas fa-hard-hat"></i>
                        </div>
                        <div>
                            <h6 class="labor-code-enhanced">${labor.laborcode || 'N/A'}</h6>
                            <p class="labor-craft-enhanced">${labor.craft || 'No craft specified'}</p>
                        </div>
                    </div>
                    <div class="labor-hours-badge">
                        <i class="fas fa-clock"></i>
                        ${labor.laborhrs || 0} hrs
                    </div>
                </div>

                <div class="labor-details-grid">
                    <div class="labor-detail-item-enhanced">
                        <div class="labor-detail-icon" style="background: linear-gradient(135deg, #28a745, #20c997);">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="labor-detail-content">
                            <p class="labor-detail-label-enhanced">Regular Hours</p>
                            <p class="labor-detail-value-enhanced">${labor.regularhrs || 0} hrs</p>
                        </div>
                    </div>

                    <div class="labor-detail-item-enhanced">
                        <div class="labor-detail-icon" style="background: linear-gradient(135deg, #ffc107, #e0a800);">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="labor-detail-content">
                            <p class="labor-detail-label-enhanced">Premium Hours</p>
                            <p class="labor-detail-value-enhanced">${labor.premiumpayhours || 0} hrs</p>
                        </div>
                    </div>

                    <div class="labor-detail-item-enhanced">
                        <div class="labor-detail-icon" style="background: linear-gradient(135deg, #6f42c1, #5a2d91);">
                            <i class="fas fa-hashtag"></i>
                        </div>
                        <div class="labor-detail-content">
                            <p class="labor-detail-label-enhanced">Trans ID</p>
                            <p class="labor-detail-value-enhanced">${labor.labtransid || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="labor-detail-item-enhanced">
                        <div class="labor-detail-icon" style="background: linear-gradient(135deg, #17a2b8, #138496);">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="labor-detail-content">
                            <p class="labor-detail-label-enhanced">Transaction Type</p>
                            <p class="labor-detail-value-enhanced">${labor.transtype || 'N/A'}</p>
                        </div>
                    </div>

                    <div class="labor-detail-item-enhanced">
                        <div class="labor-detail-icon" style="background: linear-gradient(135deg, #fd7e14, #e55a00);">
                            <i class="fas fa-calendar-plus"></i>
                        </div>
                        <div class="labor-detail-content">
                            <p class="labor-detail-label-enhanced">Start Date</p>
                            <p class="labor-detail-value-enhanced">${formatDateTime(labor.startdate)}</p>
                        </div>
                    </div>

                    <div class="labor-detail-item-enhanced">
                        <div class="labor-detail-icon" style="background: linear-gradient(135deg, #dc3545, #c82333);">
                            <i class="fas fa-calendar-minus"></i>
                        </div>
                        <div class="labor-detail-content">
                            <p class="labor-detail-label-enhanced">Finish Date</p>
                            <p class="labor-detail-value-enhanced">${formatDateTime(labor.finishdate)}</p>
                        </div>
                    </div>
                </div>

                <div class="labor-actions-enhanced">
                    <button class="labor-action-btn btn-outline-warning delete-labor-btn"
                            data-labor-id="${labor.labtransid || ''}"
                            data-labor-code="${labor.laborcode || ''}"
                            data-labor-hours="${labor.regularhrs || 0}"
                            onclick="deleteLaborEntry(this)"
                            title="Add negative hours to offset this entry">
                        <i class="fas fa-minus"></i>Subtract Hours
                    </button>
                </div>
            </div>
        `;
    });

    mobileHtml += `</div>`;

    return desktopHtml + mobileHtml;
}














function showNotification(type, message) {
    // Create notification element
    const notification = document.createElement('div');
    let alertClass = 'alert-danger'; // default to error
    if (type === 'success') alertClass = 'alert-success';
    else if (type === 'info') alertClass = 'alert-info';
    else if (type === 'warning') alertClass = 'alert-warning';

    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Global function to refresh materials after material addition
function refreshMaterials() {
    console.log('🔄 Refreshing materials after material addition...');

    // Clear materials cache first
    fetch('/api/task/planned-materials/cache/clear', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('✅ Materials cache cleared successfully');

            // Refresh all loaded materials sections
            const loadedMaterialsButtons = document.querySelectorAll('.load-materials-btn');
            loadedMaterialsButtons.forEach(button => {
                const taskWonum = button.getAttribute('data-task-wonum');
                const taskStatus = button.getAttribute('data-task-status');
                const materialsContent = document.getElementById(`materials-content-${taskWonum}`);

                // Only refresh if materials were already loaded (not showing the initial load button)
                if (materialsContent && !materialsContent.querySelector('.materials-loading') &&
                    materialsContent.innerHTML.trim() !== '' &&
                    !materialsContent.innerHTML.includes('Load Materials')) {

                    console.log(`🔄 Refreshing materials for task ${taskWonum}`);
                    loadPlannedMaterials(taskWonum, taskStatus, button);
                }
            });

            showNotification('success', 'Materials refreshed successfully');
        } else {
            console.error('❌ Failed to clear materials cache:', data.error);
            showNotification('error', 'Failed to refresh materials cache');
        }
    })
    .catch(error => {
        console.error('❌ Error clearing materials cache:', error);
        showNotification('error', 'Network error while refreshing materials');
    });
}
</script>

<!-- Include Inventory Search Modal -->
{% include 'components/inventory_search_modal.html' %}

<!-- Include Labor Search Modal -->
{% include 'components/labor_search_modal.html' %}

<!-- Include Inventory Search JavaScript -->
<script src="{{ url_for('static', filename='js/inventory_search.js') }}"></script>

<!-- Include Labor Search JavaScript -->
<script src="{{ url_for('static', filename='js/labor_search.js') }}"></script>

{% endblock %}
